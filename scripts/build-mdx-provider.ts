#!/usr/bin/env tsx

/**
 * MDX Provider Build Script
 *
 * This script builds content for the MDX provider, generating
 * static content chunks for optimal cross-platform performance.
 */

import matter from 'gray-matter'
import glob from 'fast-glob'
import fs from 'fs-extra'
import path from 'path'
import crypto from 'crypto'
import type { 
  ContentChunk, 
  ContentIndex, 
  FileMappingEntry, 
  BuildManifest,
  MDXContentItem 
} from '../src/services/content/providers/mdx/types'

/**
 * Build options
 */
interface BuildOptions {
  /** Enable watch mode for development */
  watch?: boolean
  /** Content directory path */
  contentDir?: string
  /** Output directory path */
  outputDir?: string
  /** Target platform */
  target?: string
}

/**
 * Whitelist of content directories to process
 * Only these directories will be processed by the MDX Provider
 */
const CONTENT_TYPE_WHITELIST = [
  'blogs',
  'products',
  'case-studies'
]

/**
 * Main build function
 */
async function buildMDXProvider(options: BuildOptions = {}) {
  const {
    watch: watchMode = false,
    contentDir = './content',
    outputDir = 'src/services/content/providers/mdx',
    target = process.env.DEPLOYMENT_TARGET || 'auto'
  } = options

  console.log('[MDX Provider Build] Starting build...')
  console.log(`[MDX Provider Build] Target: ${target}`)
  console.log(`[MDX Provider Build] Content dir: ${contentDir}`)
  console.log(`[MDX Provider Build] Output dir: ${outputDir}`)

  try {
    // Scan and process all content
    const contentMap = await scanAndProcessContent(contentDir)
    
    // Generate static content chunks (unified for all platforms)
    await generateStaticContentChunks(contentMap, outputDir)

    // Generate build manifest
    await generateBuildManifest({
      timestamp: new Date().toISOString(),
      target,
      staticContentGenerated: true,
      contentCount: Object.keys(contentMap).length,
      chunkCount: getChunkCount(contentMap)
    }, outputDir)
    
    console.log('[MDX Provider Build] Build completed successfully')
    
    if (watchMode) {
      console.log('[MDX Provider Build] Starting watch mode...')
      await setupWatcher(contentDir, outputDir, target)
    }
    
  } catch (error) {
    console.error('[MDX Provider Build] Build failed:', error)
    throw error
  }
}

/**
 * Scan and process all MDX content files
 */
async function scanAndProcessContent(contentDir: string): Promise<ContentChunk> {
  console.log('[MDX Provider Build] Scanning content files...')

  const contentMap: ContentChunk = {}

  // Create patterns for whitelisted content types only
  const patterns = CONTENT_TYPE_WHITELIST.map(type =>
    path.join(contentDir, type, '**/*.mdx')
  )

  const files = await glob(patterns)

  console.log(`[MDX Provider Build] Found ${files.length} content files in whitelisted directories`)
  console.log(`[MDX Provider Build] Processing content types: ${CONTENT_TYPE_WHITELIST.join(', ')}`)

  for (const filePath of files) {
    const content = await processContentFile(filePath, contentDir)
    if (content) {
      const key = generateContentKey((content as any).type, (content as any).slug, (content as any).lang || (content as any).locale)
      // Store the actual relative file path for later use
      const relativePath = path.relative(process.cwd(), filePath)
      ;(content as any)._filePath = relativePath
      contentMap[key] = content
    }
  }

  console.log(`[MDX Provider Build] Processed ${Object.keys(contentMap).length} content items`)
  return contentMap
}

/**
 * Process a single MDX content file
 */
async function processContentFile(
  filePath: string, 
  contentDir: string
): Promise<MDXContentItem | null> {
  try {
    const fileContent = await fs.readFile(filePath, 'utf-8')
    const { data, content } = matter(fileContent)
    
    // Extract metadata from file path
    const relativePath = path.relative(contentDir, filePath)
    const pathParts = relativePath.split(path.sep)

    // Expected structure: content/[type]/[locale]/[slug].mdx
    if (pathParts.length !== 3) {
      console.warn(`[MDX Provider Build] Invalid file structure: ${filePath}`)
      return null
    }

    const typeDir = pathParts[0] // e.g., 'blogs'
    const type = normalizeContentType(typeDir) // e.g., 'blog'
    const locale = pathParts[1]
    const fileSlug = path.basename(pathParts[pathParts.length - 1], '.mdx')

    // Verify the type is in our whitelist
    if (!CONTENT_TYPE_WHITELIST.includes(typeDir)) {
      console.warn(`[MDX Provider Build] Content type '${typeDir}' not in whitelist, skipping: ${filePath}`)
      return null
    }
    
    // Use slug from frontmatter if available, otherwise use filename
    const slug = data.slug || fileSlug
    
    // Build MDX content item with proper type structure
    const baseItem = {
      slug,
      title: data.title || slug,
      lang: locale,
      url: `/${locale}/${getContentDirName(type)}/${slug}`,
      description: data.description || '',
      body: {
        mdx: content,
        // Component will be set at runtime
        component: undefined,
        // HTML could be generated here if needed
        html: undefined
      },
      coverImage: data.coverImage || '',
      authorImage: data.authorImage || '',
      videoUrl: data.videoUrl || '',
      videoThumbnail: data.videoThumbnail || '',
      videoDuration: data.videoDuration || '',
      author: data.author || '',
      publishedAt: data.publishedAt || data.date || new Date().toISOString(),
      createdAt: data.createdAt || new Date().toISOString(),
      featured: data.featured || false,
      tags: data.tags || [],
      // Copy any additional frontmatter fields
      ...data
    }

    // Add type-specific properties
    let contentItem: MDXContentItem
    if (type === 'blog') {
      contentItem = { ...baseItem, type: 'blog' } as MDXContentItem
    } else if (type === 'product') {
      contentItem = { ...baseItem, type: 'product', icon: data.icon } as MDXContentItem
    } else if (type === 'case-study') {
      contentItem = { ...baseItem, type: 'case-study' } as MDXContentItem
    } else {
      console.warn(`[MDX Provider Build] Unknown content type: ${type}`)
      return null
    }
    
    return contentItem
    
  } catch (error) {
    console.error(`[MDX Provider Build] Failed to process ${filePath}:`, error)
    return null
  }
}

/**
 * Generate static content chunks for unified strategy
 */
async function generateStaticContentChunks(
  contentMap: ContentChunk, 
  outputDir: string
): Promise<void> {
  console.log('[MDX Provider Build] Generating static content chunks...')
  
  const staticContentDir = path.join(outputDir, 'static-content')
  await fs.ensureDir(staticContentDir)
  
  // Group content by type
  const contentChunks: Record<string, ContentChunk> = {
    blogs: {},
    products: {},
    'case-studies': {}
  }
  
  // Distribute content into chunks
  for (const [key, content] of Object.entries(contentMap)) {
    const chunkKey = getChunkKeyFromContentType((content as any).type)
    if (contentChunks[chunkKey]) {
      contentChunks[chunkKey][key] = content
    }
  }
  
  // Generate chunk files
  const generatedChunks: Array<{ chunkName: string; variableName: string }> = []
  
  for (const [chunkName, chunkContent] of Object.entries(contentChunks)) {
    if (Object.keys(chunkContent).length === 0) continue
    
    const variableName = chunkName.replace(/-/g, '')
    const chunkFileName = `static-content-${chunkName}.ts`
    const chunkFilePath = path.join(staticContentDir, chunkFileName)
    
    const chunkFileContent = `/**
 * Static Content Chunk: ${chunkName}
 * 
 * This file is auto-generated by build-mdx-provider.ts
 * DO NOT EDIT MANUALLY
 * 
 * Last generated: ${new Date().toISOString()}
 */

import type { ContentChunk } from '../types'

const ${variableName}Content: ContentChunk = ${JSON.stringify(chunkContent, null, 2)}

// Export default for dynamic imports
export default ${variableName}Content
`
    
    await fs.writeFile(chunkFilePath, chunkFileContent, 'utf-8')
    console.log(`[MDX Provider Build] Generated chunk: ${chunkFileName} (${Object.keys(chunkContent).length} items)`)
    
    generatedChunks.push({ chunkName, variableName })
  }
  
  // Generate main static content index
  await generateStaticContentIndex(generatedChunks, staticContentDir)
}

/**
 * Generate static content index file
 */
async function generateStaticContentIndex(
  chunks: Array<{ chunkName: string; variableName: string }>,
  staticContentDir: string
): Promise<void> {
  const indexFilePath = path.join(staticContentDir, 'static-content.ts')
  
  const importStatements = chunks.map(({ chunkName, variableName }) =>
    `import('./static-content-${chunkName}').catch(() => ({ default: {} }))`
  ).join(',\n      ')
  
  const destructuringArray = chunks.map(({ variableName }) => variableName).join(', ')
  
  const combineStatements = chunks.map(({ variableName }) =>
    `...${variableName}.default`
  ).join(',\n      ')
  
  const indexFileContent = `/**
 * Static Content Index for MDX Provider
 *
 * This file is auto-generated by build-mdx-provider.ts
 * DO NOT EDIT MANUALLY
 *
 * Last generated: ${new Date().toISOString()}
 */

import type { MDXContentItem } from '../types'

// Lazy-loaded content chunks
let contentCache: Record<string, MDXContentItem> | null = null

/**
 * Get all static content with lazy loading and caching
 *
 * @returns Complete content bundle
 */
export async function getStaticContent(): Promise<Record<string, MDXContentItem>> {
  // Return cached content if available
  if (contentCache) {
    return contentCache
  }

  console.log('[StaticContent] Loading content chunks...')

  try {
    // Dynamically import all content chunks
    const [${destructuringArray}] = await Promise.all([
      ${importStatements}
    ])

    // Combine all chunks
    contentCache = {
      ${combineStatements}
    }

    console.log(\`[StaticContent] Loaded \${Object.keys(contentCache).length} content items\`)
    return contentCache
  } catch (error) {
    console.error('[StaticContent] Failed to load content chunks:', error)
    return {}
  }
}
`
  
  await fs.writeFile(indexFilePath, indexFileContent, 'utf-8')
  console.log('[MDX Provider Build] Generated static content index')
}



/**
 * Generate build manifest
 */
async function generateBuildManifest(
  manifest: BuildManifest,
  outputDir: string
): Promise<void> {
  const manifestPath = path.join(outputDir, 'generated', 'build-manifest.json')
  await fs.ensureDir(path.dirname(manifestPath))
  await fs.writeJSON(manifestPath, manifest, { spaces: 2 })
  console.log('[MDX Provider Build] Generated build manifest')
}

/**
 * Setup file watcher for development
 */
async function setupWatcher(
  contentDir: string,
  outputDir: string,
  target: string
): Promise<void> {
  // Dynamic import chokidar to avoid issues if not installed
  let chokidar: any
  try {
    chokidar = await import('chokidar')
  } catch (error) {
    console.error('[MDX Provider Build] chokidar not available, watch mode disabled')
    return
  }

  // Watch only whitelisted content directories
  const watchPatterns = CONTENT_TYPE_WHITELIST.map(type =>
    path.join(contentDir, type, '**/*.mdx')
  )

  const watcher = chokidar.watch(watchPatterns, {
    ignored: /node_modules/,
    persistent: true
  })
  
  const rebuild = async () => {
    try {
      console.log('[MDX Provider Build] Content changed, rebuilding...')
      const contentMap = await scanAndProcessContent(contentDir)
      await generateStaticContentChunks(contentMap, outputDir)

      console.log('[MDX Provider Build] Rebuild completed')
    } catch (error) {
      console.error('[MDX Provider Build] Rebuild failed:', error)
    }
  }
  
  watcher.on('change', rebuild)
  watcher.on('add', rebuild)
  watcher.on('unlink', rebuild)
  
  console.log('[MDX Provider Build] Watching for content changes...')
}

// Utility functions

function normalizeContentType(typeDir: string): string {
  // Map directory names to content types
  const typeMap: Record<string, string> = {
    'blogs': 'blog',
    'products': 'product',
    'case-studies': 'case-study'
  }

  return typeMap[typeDir] || typeDir
}

function getChunkKeyFromContentType(type: string): string {
  return type === 'case-study' ? 'case-studies' : `${type}s`
}

function generateContentKey(type: string, slug: string, locale: string): string {
  return `${type}-${slug}-${locale}`
}

function getChunkCount(contentMap: ContentChunk): number {
  const chunks = new Set<string>()
  for (const content of Object.values(contentMap)) {
    chunks.add(getChunkKeyFromContentType((content as any).type))
  }
  return chunks.size
}

function generateChecksum(content: string): string {
  return crypto.createHash('md5').update(content).digest('hex')
}

function getContentDirName(type: string): string {
  // Map content types to actual directory names
  const dirMap: Record<string, string> = {
    'blog': 'blogs',
    'product': 'products',
    'case-study': 'case-studies'
  }

  return dirMap[type] || type
}

// Main execution
if (require.main === module) {
  const isWatch = process.argv.includes('--watch') || process.argv.includes('-w')
  
  buildMDXProvider({ watch: isWatch }).catch(error => {
    console.error('[MDX Provider Build] Fatal error:', error)
    process.exit(1)
  })
}

export { buildMDXProvider }
