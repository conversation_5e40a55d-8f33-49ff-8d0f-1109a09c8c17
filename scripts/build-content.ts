#!/usr/bin/env tsx

/**
 * MDX Content Build Script
 *
 * This script builds content using the MDX provider.
 * The contentlayer2 provider has been removed as part of the
 * content management system refactoring.
 */

import { execSync } from 'child_process'
import path from 'path'
import dotenv from 'dotenv'

// Load environment variables from .env files
dotenv.config({ path: '.env.development' })
dotenv.config({ path: '.env.local' })
dotenv.config({ path: '.env' })

/**
 * Main build function
 */
async function buildContent() {
  const isWatch = process.argv.includes('--watch') || process.argv.includes('-w')

  console.log('[Content Build] Provider: mdx')
  console.log(`[Content Build] Mode: ${isWatch ? 'watch' : 'build'}`)

  try {
    await buildMDXProvider(isWatch)
  } catch (error) {
    console.error('[Content Build] Build failed:', error)
    process.exit(1)
  }
}


/**
 * Build content with MDX provider
 *
 * @param watch - Enable watch mode
 */
async function buildMDXProvider(watch: boolean) {
  console.log('[Content Build] Running MDX Provider build...')

  // Run the MDX provider build script
  const buildScriptPath = path.join(__dirname, 'build-mdx-provider.ts')
  const args = watch ? ['--watch'] : []

  execSync(`tsx ${buildScriptPath} ${args.join(' ')}`, {
    stdio: 'inherit',
    env: { ...process.env }
  })
}

// Run the build
buildContent().catch(error => {
  console.error('[Content Build] Fatal error:', error)
  process.exit(1)
})