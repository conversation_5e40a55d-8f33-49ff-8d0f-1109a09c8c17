/**
 * RSS Feed Generation Script
 *
 * This script generates RSS feeds for blog content in multiple languages.
 * Each language gets its own RSS feed with properly formatted XML structure
 * and locale-specific metadata.
 */

import { writeFileSync } from 'fs'
import { contentService } from '../src/services/content'

const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'

/**
 * Generate RSS feed for a specific locale
 *
 * Creates an RSS 2.0 compliant feed with the latest blog posts for the
 * specified language. Includes proper metadata, publication dates, and
 * category tags.
 *
 * @param locale - Language locale (e.g., 'en', 'zh')
 */
async function generateRSSFeed(locale: string) {
  // Get all blogs and filter by locale, get latest 20 posts
  const allBlogs = await contentService.getContentForStaticGeneration('blog')
  const blogs = allBlogs
    .filter(blog => blog.lang === locale)
    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())
    .slice(0, 20) // Limit to latest 20 posts for performance

  // Locale-specific site metadata
  const siteTitle = locale === 'zh' ? 'ShipAny 博客' : 'ShipAny Blog'
  const siteDescription = locale === 'zh'
    ? 'ShipAny - 几小时内构建任何 AI SaaS 创业项目的最新资讯和教程'
    : 'ShipAny - Latest news and tutorials for building AI SaaS startups in hours'

  // Generate feed and site URLs with proper locale handling
  const feedUrl = locale === 'en' ? `${baseUrl}/rss.xml` : `${baseUrl}/rss-${locale}.xml`
  const siteUrl = locale === 'en' ? baseUrl : `${baseUrl}/${locale}`

  // Initialize RSS XML with proper structure and metadata
  let rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>${siteTitle}</title>
    <description>${siteDescription}</description>
    <link>${siteUrl}</link>
    <atom:link href="${feedUrl}" rel="self" type="application/rss+xml" />
    <language>${locale === 'zh' ? 'zh-CN' : 'en-US'}</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
`

  // Add each blog post as an RSS item
  blogs.forEach(blog => {
    // Generate post URL with proper locale handling
    const postUrl = locale === 'en' ? `${baseUrl}/blogs/${blog.slug}` : `${baseUrl}/${locale}/blogs/${blog.slug}`
    const pubDate = new Date(blog.publishedAt || blog.createdAt).toUTCString()

    rss += `    <item>
      <title><![CDATA[${blog.title}]]></title>
      <description><![CDATA[${blog.description || ''}]]></description>
      <link>${postUrl}</link>
      <guid isPermaLink="true">${postUrl}</guid>
      <pubDate>${pubDate}</pubDate>
`

    // Add author information if available
    if (blog.author) {
      rss += `      <author>${blog.author}</author>
`
    }

    // Add category tags if available
    if (blog.tags && blog.tags.length > 0) {
      blog.tags.forEach(tag => {
        rss += `      <category>${tag}</category>
`
      })
    }

    rss += `    </item>
`
  })

  // Close RSS XML structure
  rss += `  </channel>
</rss>`

  // Write RSS feed to appropriate file
  const filename = locale === 'en' ? 'public/rss.xml' : `public/rss-${locale}.xml`
  writeFileSync(filename, rss)
  console.log(`✅ RSS feed for ${locale} generated successfully!`)
}

/**
 * Generate RSS feeds for all supported locales
 *
 * Creates separate RSS files for each language:
 * - English: /rss.xml
 * - Other languages: /rss-{locale}.xml
 */
async function generateAllRSSFeeds() {
  const locales = ['en', 'zh']

  try {
    for (const locale of locales) {
      await generateRSSFeed(locale)
    }
    console.log('✅ All RSS feeds generated successfully!')
  } catch (error) {
    console.error('[RSS Generation] Fatal error:', error)
    process.exit(1)
  }
}

// Execute RSS generation
generateAllRSSFeeds()
