/**
 * Markdown Renderer
 * 
 * A provider-agnostic component that renders raw markdown/MDX content.
 * Supports both standard markdown and MDX with custom components.
 */

'use client'

import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { Video, YouTube, Bilibili, VideoGallery } from './video-components'
import React from 'react'

interface MarkdownRendererProps {
  source: string
  components: Record<string, any>
  className?: string
}

/**
 * Extract VideoGallery components from content and render them separately
 */
function extractVideoGalleries(content: string): { content: string; galleries: Array<{ id: string; props: any }> } {
  const galleries: Array<{ id: string; props: any }> = []
  let processedContent = content

  // Find VideoGallery components
  const videoGalleryRegex = /<VideoGallery[\s\S]*?videos=\{(\[[\s\S]*?\])\}[\s\S]*?\/>/g
  let match
  let galleryIndex = 0

  while ((match = videoGalleryRegex.exec(content)) !== null) {
    const galleryId = `video-gallery-${galleryIndex++}`
    const videosArrayStr = match[1]

    try {
      // Parse the videos array (simplified parsing)
      const videos = parseVideosArray(videosArrayStr)
      const columnsMatch = match[0].match(/columns=\{(\d+)\}/)
      const columns = columnsMatch ? parseInt(columnsMatch[1]) : 2

      galleries.push({
        id: galleryId,
        props: { videos, columns }
      })

      // Replace with placeholder
      processedContent = processedContent.replace(match[0], `<div id="${galleryId}"></div>`)
    } catch (e) {
      console.warn('Failed to parse VideoGallery:', e)
      processedContent = processedContent.replace(match[0], '<div class="video-gallery-error">VideoGallery parsing error</div>')
    }
  }

  return { content: processedContent, galleries }
}

/**
 * Parse videos array from string format
 */
function parseVideosArray(videosStr: string): any[] {
  try {
    // This is a simplified parser - in production you might want a more robust solution
    // For now, we'll create a basic parser for the expected format
    const videos: any[] = []

    // Extract individual video objects
    const videoMatches = videosStr.match(/\{[^}]*\}/g)
    if (videoMatches) {
      videoMatches.forEach(videoStr => {
        const video: any = {}

        // Extract type
        const typeMatch = videoStr.match(/type:\s*["']([^"']+)["']/)
        if (typeMatch) video.type = typeMatch[1]

        // Extract src
        const srcMatch = videoStr.match(/src:\s*["']([^"']+)["']/)
        if (srcMatch) video.src = srcMatch[1]

        // Extract videoId
        const videoIdMatch = videoStr.match(/videoId:\s*["']([^"']+)["']/)
        if (videoIdMatch) video.videoId = videoIdMatch[1]

        // Extract poster
        const posterMatch = videoStr.match(/poster:\s*["']([^"']+)["']/)
        if (posterMatch) video.poster = posterMatch[1]

        // Extract title
        const titleMatch = videoStr.match(/title:\s*["']([^"']+)["']/)
        if (titleMatch) video.title = titleMatch[1]

        if (video.type) {
          videos.push(video)
        }
      })
    }

    return videos
  } catch (e) {
    console.warn('Failed to parse videos array:', e)
    return []
  }
}

/**
 * Process MDX-style components in markdown content
 * Converts <ComponentName prop="value" /> to HTML that can be rendered
 */
function processMDXComponents(content: string): string {
  let processed = content

  // Handle Video components
  processed = processed.replace(
    /<Video\s+([^>]*?)\/>/g,
    (match, props) => {
      // Extract props using regex
      const srcMatch = props.match(/src=["']([^"']+)["']/)
      const posterMatch = props.match(/poster=["']([^"']+)["']/)
      const src = srcMatch ? srcMatch[1] : ''
      const poster = posterMatch ? posterMatch[1] : ''

      // Return HTML video element
      return `<video src="${src}" poster="${poster}" controls class="w-full rounded-lg">Your browser does not support the video tag.</video>`
    }
  )
  
  // Handle YouTube components
  processed = processed.replace(
    /<YouTube\s+([^>]*?)\/>/g,
    (match, props) => {
      const videoIdMatch = props.match(/videoId=["']([^"']+)["']/)
      const titleMatch = props.match(/title=["']([^"']+)["']/)
      const videoId = videoIdMatch ? videoIdMatch[1] : ''
      const title = titleMatch ? titleMatch[1] : ''
      
      return `<iframe src="https://www.youtube.com/embed/${videoId}" title="${title}" class="w-full aspect-video rounded-lg" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>`
    }
  )
  
  // Handle Bilibili components
  processed = processed.replace(
    /<Bilibili\s+([^>]*?)\/>/g,
    (match, props) => {
      const bvidMatch = props.match(/bvid=["']([^"']+)["']/)
      const titleMatch = props.match(/title=["']([^"']+)["']/)
      const bvid = bvidMatch ? bvidMatch[1] : ''
      const title = titleMatch ? titleMatch[1] : ''
      
      return `<iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=${bvid}" title="${title}" class="w-full aspect-video rounded-lg" frameborder="0" allowfullscreen></iframe>`
    }
  )
  

  
  return processed
}

/**
 * Enhanced components mapping that includes HTML element handlers
 */
const createComponents = (baseComponents: Record<string, any>) => ({
  ...baseComponents,
  // Custom handlers for HTML elements
  video: ({ node, ...props }: any) => (
    <video {...props} />
  ),
  iframe: ({ node, ...props }: any) => (
    <iframe {...props} />
  ),
})

/**
 * Generic markdown renderer that works with any provider
 * This provides full markdown support with custom component mappings
 */
export default function MarkdownRenderer({ source, components, className }: MarkdownRendererProps) {
  // Extract VideoGallery components and process other MDX components
  const { content: processedContent, galleries } = extractVideoGalleries(source)
  const processedSource = processMDXComponents(processedContent)

  // Use effect to render VideoGallery components after the main content is rendered
  React.useEffect(() => {
    galleries.forEach(({ id, props }) => {
      const element = document.getElementById(id)
      if (element) {
        // Create a React root and render the VideoGallery component
        import('react-dom/client').then(({ createRoot }) => {
          const root = createRoot(element)
          root.render(React.createElement(VideoGallery, props))
        }).catch(console.error)
      }
    })
  }, [galleries])

  return (
    <div className={cn('prose prose-neutral dark:prose-invert max-w-none', className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={createComponents(components)}
      >
        {processedSource}
      </ReactMarkdown>
    </div>
  )
}