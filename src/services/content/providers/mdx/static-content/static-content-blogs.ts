/**
 * Static Content Chunk: blogs
 * 
 * This file is auto-generated by build-mdx-provider.ts
 * DO NOT EDIT MANUALLY
 * 
 * Last generated: 2025-07-26T13:24:29.117Z
 */

import type { ContentChunk } from '../types'

const blogsContent: ContentChunk = {
  "blog-english-only-test-en": {
    "slug": "english-only-test",
    "title": "English Only Test Article",
    "lang": "en",
    "url": "/en/blogs/english-only-test",
    "description": "This article is only available in English to test the language switching fallback functionality.",
    "body": {
      "mdx": "\n# English Only Test Article\n\nThis article is specifically created to test the language switching functionality when content is not available in all languages.\n\n## Purpose\n\nWhen a user tries to switch to Chinese (中文) while viewing this article, they should be redirected to the Chinese blog list page since this article doesn't have a Chinese version.\n\n## Testing Scenarios\n\n1. **Direct Language Switch**: Use the header language selector\n2. **Language Versions Indicator**: Use the language versions component on this page\n3. **Fallback Behavior**: Verify that users are redirected appropriately\n\n## Expected Behavior\n\n- ✅ English version should be accessible\n- ❌ Chinese version should not exist\n- 🔄 Switching to Chinese should redirect to `/zh/blogs` with a notification\n\nThis helps ensure our intelligent language switching system works correctly in all scenarios.\n"
    },
    "coverImage": "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop",
    "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    "videoUrl": "",
    "videoThumbnail": "",
    "videoDuration": "",
    "author": "Test Author",
    "publishedAt": "2025-01-17",
    "createdAt": "2025-07-26T13:24:29.102Z",
    "featured": false,
    "tags": [
      "test",
      "english-only",
      "language-switching"
    ],
    "type": "blog",
    "_filePath": "content/blogs/en/english-only-test.mdx"
  },
  "blog-getting-started-with-shipany-en": {
    "slug": "getting-started-with-shipany",
    "title": "Getting Started with ShipAny: Build Your AI SaaS in Hours",
    "lang": "en",
    "url": "/en/blogs/getting-started-with-shipany",
    "description": "Learn how to quickly build and deploy your AI SaaS application using ShipAny's powerful template and components.",
    "body": {
      "mdx": "\n# Getting Started with ShipAny\n\nWelcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.\n\n## What is ShipAny?\n\nShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.\n\n## Key Features\n\n- **🚀 Rapid Development**: Pre-built components and templates\n- **🤖 AI Integration**: Ready-to-use AI SDK integrations\n- **💳 Payment Processing**: Stripe integration out of the box\n- **🌍 Internationalization**: Multi-language support\n- **📱 Responsive Design**: Mobile-first approach\n- **🔐 Authentication**: Secure user management\n\n## Quick Start\n\n### 1. Clone the Repository\n\n```bash\ngit clone https://github.com/shipany/shipany-template\ncd shipany-template\n```\n\n### 2. Install Dependencies\n\n```bash\npnpm install\n```\n\n### 3. Set Up Environment Variables\n\nCreate a `.env.local` file with your configuration:\n\n```env\nNEXT_PUBLIC_WEB_URL=http://localhost:3000\nDATABASE_URL=your_database_url\nNEXTAUTH_SECRET=your_secret\nSTRIPE_SECRET_KEY=your_stripe_key\n```\n\n### 4. Run the Development Server\n\n```bash\npnpm dev\n```\n\n## Building Your First Feature\n\nLet's create a simple AI-powered text generator:\n\n### 1. Create the API Route\n\n```typescript\n// app/api/generate/route.ts\nimport { openai } from '@ai-sdk/openai'\nimport { generateText } from 'ai'\n\nexport async function POST(request: Request) {\n  const { prompt } = await request.json()\n  \n  const { text } = await generateText({\n    model: openai('gpt-3.5-turbo'),\n    prompt: `Generate creative content based on: ${prompt}`,\n  })\n  \n  return Response.json({ text })\n}\n```\n\n### 2. Create the Frontend Component\n\n```tsx\n'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\n\nexport function TextGenerator() {\n  const [prompt, setPrompt] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const handleGenerate = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt }),\n      })\n      const data = await response.json()\n      setResult(data.text)\n    } catch (error) {\n      console.error('Error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <Textarea\n        placeholder=\"Enter your prompt...\"\n        value={prompt}\n        onChange={(e) => setPrompt(e.target.value)}\n      />\n      <Button onClick={handleGenerate} disabled={loading}>\n        {loading ? 'Generating...' : 'Generate'}\n      </Button>\n      {result && (\n        <div className=\"p-4 bg-muted rounded-lg\">\n          {result}\n        </div>\n      )}\n    </div>\n  )\n}\n```\n\n## Next Steps\n\nNow that you have the basics set up, you can:\n\n1. **Customize the UI**: Modify components to match your brand\n2. **Add More AI Features**: Integrate additional AI models\n3. **Set Up Payments**: Configure Stripe for subscriptions\n4. **Deploy**: Deploy to Vercel or your preferred platform\n\n## Conclusion\n\nShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.\n\nReady to ship your next AI SaaS? Get started with ShipAny today!\n"
    },
    "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop",
    "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    "videoUrl": "",
    "videoThumbnail": "",
    "videoDuration": "",
    "author": "ShipAny Team",
    "publishedAt": "2025-01-17",
    "createdAt": "2025-07-26T13:24:29.104Z",
    "featured": true,
    "tags": [
      "tutorial",
      "getting-started",
      "ai-saas"
    ],
    "type": "blog",
    "_filePath": "content/blogs/en/getting-started-with-shipany.mdx"
  },
  "blog-getting-started-with-shipany-zh": {
    "slug": "getting-started-with-shipany",
    "title": "ShipAny 快速入门：几小时内构建你的 AI SaaS",
    "lang": "zh",
    "url": "/zh/blogs/getting-started-with-shipany",
    "description": "学习如何使用 ShipAny 强大的模板和组件快速构建和部署你的 AI SaaS 应用程序。",
    "body": {
      "mdx": "\n# ShipAny 快速入门\n\n欢迎使用 ShipAny！这份综合指南将带你使用我们强大的模板系统构建你的第一个 AI SaaS 应用程序。\n\n## 什么是 ShipAny？\n\nShipAny 是一个专为快速高效构建 AI SaaS 创业项目而设计的 Next.js 样板。通过预构建的组件、身份验证、支付处理和 AI 集成，你可以专注于你的独特价值主张，而不是样板代码。\n\n## 核心特性\n\n- **🚀 快速开发**：预构建的组件和模板\n- **🤖 AI 集成**：开箱即用的 AI SDK 集成\n- **💳 支付处理**：内置 Stripe 集成\n- **🌍 国际化**：多语言支持\n- **📱 响应式设计**：移动优先的方法\n- **🔐 身份验证**：安全的用户管理\n\n## 快速开始\n\n### 1. 克隆仓库\n\n```bash\ngit clone https://github.com/shipany/shipany-template\ncd shipany-template\n```\n\n### 2. 安装依赖\n\n```bash\npnpm install\n```\n\n### 3. 设置环境变量\n\n创建一个 `.env.local` 文件并配置：\n\n```env\nNEXT_PUBLIC_WEB_URL=http://localhost:3000\nDATABASE_URL=your_database_url\nNEXTAUTH_SECRET=your_secret\nSTRIPE_SECRET_KEY=your_stripe_key\n```\n\n### 4. 运行开发服务器\n\n```bash\npnpm dev\n```\n\n## 构建你的第一个功能\n\n让我们创建一个简单的 AI 驱动的文本生成器：\n\n### 1. 创建 API 路由\n\n```typescript\n// app/api/generate/route.ts\nimport { openai } from '@ai-sdk/openai'\nimport { generateText } from 'ai'\n\nexport async function POST(request: Request) {\n  const { prompt } = await request.json()\n  \n  const { text } = await generateText({\n    model: openai('gpt-3.5-turbo'),\n    prompt: `基于以下内容生成创意文本：${prompt}`,\n  })\n  \n  return Response.json({ text })\n}\n```\n\n### 2. 创建前端组件\n\n```tsx\n'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\n\nexport function TextGenerator() {\n  const [prompt, setPrompt] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const handleGenerate = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt }),\n      })\n      const data = await response.json()\n      setResult(data.text)\n    } catch (error) {\n      console.error('错误:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <Textarea\n        placeholder=\"输入你的提示...\"\n        value={prompt}\n        onChange={(e) => setPrompt(e.target.value)}\n      />\n      <Button onClick={handleGenerate} disabled={loading}>\n        {loading ? '生成中...' : '生成'}\n      </Button>\n      {result && (\n        <div className=\"p-4 bg-muted rounded-lg\">\n          {result}\n        </div>\n      )}\n    </div>\n  )\n}\n```\n\n## 下一步\n\n现在你已经设置好了基础，你可以：\n\n1. **自定义 UI**：修改组件以匹配你的品牌\n2. **添加更多 AI 功能**：集成额外的 AI 模型\n3. **设置支付**：为订阅配置 Stripe\n4. **部署**：部署到 Vercel 或你首选的平台\n\n## 结论\n\nShipAny 提供了快速构建和启动 AI SaaS 所需的一切。凭借其全面的功能集和开发者友好的架构，你可以专注于最重要的事情：为用户构建优秀的产品。\n\n准备好发布你的下一个 AI SaaS 了吗？今天就开始使用 ShipAny 吧！\n"
    },
    "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop",
    "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    "videoUrl": "",
    "videoThumbnail": "",
    "videoDuration": "",
    "author": "ShipAny 团队",
    "publishedAt": "2025-01-17",
    "createdAt": "2025-07-26T13:24:29.105Z",
    "featured": true,
    "tags": [
      "教程",
      "快速入门",
      "ai-saas"
    ],
    "type": "blog",
    "_filePath": "content/blogs/zh/shipany-kuai-su-ru-men.mdx"
  }
}

// Export default for dynamic imports
export default blogsContent
