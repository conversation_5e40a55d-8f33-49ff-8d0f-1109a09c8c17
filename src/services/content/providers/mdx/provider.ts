/**
 * MDX Content Provider Implementation
 *
 * Simplified implementation that directly handles content loading with
 * platform-adaptive caching for optimal performance across all environments.
 *
 * This provider loads pre-compiled static content chunks optimized for
 * both Cloudflare Workers and Node.js environments.
 */

import type {
  ContentProvider,
  ContentItem,
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion
} from '../../types'
import type { MDXProviderConfig, ContentChunk } from './types'
import { mergeConfig } from './config'
import { logPlatformInfo } from './utils/platform-detector'
import { PlatformAdaptiveCache } from './cache/platform-adaptive-cache'

/**
 * MDX Provider Class
 *
 * Directly implements content loading using precompiled static chunks
 * with platform-adaptive caching for optimal performance.
 */
export class MDXProvider implements ContentProvider {
  readonly name = 'mdx'
  readonly version = '1.0.0'

  private cache: PlatformAdaptiveCache
  private config: Required<MDXProviderConfig>
  private contentChunks = new Map<string, Promise<ContentChunk>>()
  private loadedChunks = new Set<string>()

  constructor(config?: Partial<MDXProviderConfig>) {
    // Merge user config with defaults
    this.config = mergeConfig(config)

    // Initialize platform-adaptive cache
    this.cache = new PlatformAdaptiveCache({
      ttl: this.config.cacheTTL,
      enableStats: this.config.developmentMode
    })

    // Log platform information in development
    if (this.config.developmentMode) {
      logPlatformInfo()
    }

    console.log(`[MDXProvider] Initialized with direct precompiled content loading`)
  }
  
  /**
   * Get a single content item by type, slug, and locale
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    if (!this.isValidContentType(type) || !this.isValidLocale(locale)) {
      return null
    }

    const contentKey = this.generateContentKey(type, slug, locale)

    try {
      // Check cache first
      const cached = await this.cache.get<T>(contentKey)
      if (cached) {
        return cached
      }

      // Load from static chunks
      const content = await this.loadContentFromChunks<T>(type, slug, locale)

      // Cache the result
      if (content) {
        await this.cache.set(contentKey, content)
      }

      return content
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content ${type}/${slug}/${locale}:`, error)
      return null
    }
  }

  /**
   * Get a list of content items with optional filtering and sorting
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    if (!this.isValidContentType(type) || !this.isValidLocale(locale)) {
      return []
    }

    const cacheKey = `list-${type}-${locale}-${JSON.stringify(options || {})}`

    try {
      // Check cache first
      const cached = await this.cache.get<T[]>(cacheKey)
      if (cached) {
        return cached
      }

      // Load all content of this type
      const chunk = await this.loadChunk(this.getChunkKey(type))
      const items: T[] = []

      for (const [key, item] of Object.entries(chunk)) {
        if (key.startsWith(`${type}-`) && key.endsWith(`-${locale}`)) {
          items.push(item as unknown as T)
        }
      }

      // Apply filtering and sorting
      const filtered = this.filterAndSortContent(items, options)

      // Cache the result
      await this.cache.set(cacheKey, filtered, 1800) // 30 minutes for lists

      return filtered
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content list ${type}/${locale}:`, error)
      return []
    }
  }

  /**
   * Get all content for static generation
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    if (!this.isValidContentType(type)) {
      return []
    }

    try {
      const chunk = await this.loadChunk(this.getChunkKey(type))
      const items: T[] = []

      for (const [key, item] of Object.entries(chunk)) {
        if (key.startsWith(`${type}-`)) {
          items.push(item as unknown as T)
        }
      }

      return items
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content for static generation ${type}:`, error)
      return []
    }
  }

  /**
   * Get all content slugs for static path generation
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    if (!this.isValidContentType(type)) {
      return []
    }

    try {
      const chunk = await this.loadChunk(this.getChunkKey(type))
      const slugs: Array<{ locale: string; slug: string }> = []

      for (const [key, item] of Object.entries(chunk)) {
        if (key.startsWith(`${type}-`)) {
          slugs.push({
            locale: item.lang,
            slug: item.slug
          })
        }
      }

      return slugs
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content slugs ${type}:`, error)
      return []
    }
  }

  /**
   * Check if content exists
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    try {
      const content = await this.getContent(type, slug, locale)
      return content !== null
    } catch (error) {
      console.error(`[MDXProvider] Failed to check content existence ${type}/${slug}/${locale}:`, error)
      return false
    }
  }

  /**
   * Get content title only
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    try {
      const content = await this.getContent(type, slug, locale)
      return content?.title || null
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content title ${type}/${slug}/${locale}:`, error)
      return null
    }
  }

  /**
   * Get content metadata
   */
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    try {
      const content = await this.getContent(type, slug, locale)

      if (!content) {
        return null
      }

      // Calculate word count and reading time
      const mdxContent = (content.body as any).mdx || ''
      const wordCount = mdxContent.split(/\s+/).filter((word: string) => word.length > 0).length
      const readingTime = Math.ceil(wordCount / 200) // Assuming 200 words per minute

      return {
        wordCount,
        readingTime,
        publishedAt: content.publishedAt,
        updatedAt: (content as any).updatedAt,
        author: content.author,
        tags: content.tags || []
      }
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content metadata ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Get available language versions for content
   */
  async getLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    try {
      const allSlugs = await this.getAllContentSlugs(type)
      const versions: LanguageVersion[] = []

      for (const item of allSlugs.filter(item => item.slug === slug)) {
        const title = await this.getContentTitle(type, slug, item.locale)
        versions.push({
          lang: item.locale,
          title: title || slug,
          url: `/${item.locale}/${this.getContentTypePath(type)}/${slug}`,
          available: true
        })
      }

      return versions
    } catch (error) {
      console.error(`[MDXProvider] Failed to get language versions ${type}/${slug}:`, error)
      return []
    }
  }

  /**
   * Get provider information
   */
  getProviderInfo() {
    return {
      name: this.name,
      version: this.version,
      strategy: 'precompiled',
      config: {
        contentDir: this.config.contentDir,
        cacheTTL: this.config.cacheTTL,
        developmentMode: this.config.developmentMode
      }
    }
  }

  /**
   * Get provider statistics
   */
  getStats() {
    return {
      provider: this.name,
      strategy: 'precompiled',
      version: this.version,
      // Additional stats could be added here
    }
  }

  /**
   * Get available language versions of content
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    // Get all supported locales
    const supportedLocales = ['en', 'zh'] // TODO: Make this configurable
    const availableLanguages: LanguageVersion[] = []

    for (const locale of supportedLocales) {
      const exists = await this.contentExists(type, slug, locale)
      if (exists) {
        const title = await this.getContentTitle(type, slug, locale)
        availableLanguages.push({
          lang: locale,
          title: title || slug,
          url: `/${locale}/${this.getContentTypePath(type)}/${slug}`,
          available: true
        })
      }
    }

    return availableLanguages
  }

  /**
   * Load content from static chunks
   */
  private async loadContentFromChunks<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    const chunkKey = this.getChunkKey(type)
    const chunk = await this.loadChunk(chunkKey)
    const contentKey = this.generateContentKey(type, slug, locale)

    return (chunk[contentKey] as unknown as T) || null
  }

  /**
   * Load a specific content chunk
   */
  private async loadChunk(chunkKey: string): Promise<ContentChunk> {
    // Return cached promise if already loading
    if (this.contentChunks.has(chunkKey)) {
      return this.contentChunks.get(chunkKey)!
    }

    // Create loading promise
    const loadingPromise = this.loadChunkFromStatic(chunkKey)
    this.contentChunks.set(chunkKey, loadingPromise)

    try {
      const chunk = await loadingPromise
      this.loadedChunks.add(chunkKey)
      return chunk
    } catch (error) {
      // Remove failed promise so it can be retried
      this.contentChunks.delete(chunkKey)
      throw error
    }
  }

  /**
   * Load chunk from static imports
   */
  private async loadChunkFromStatic(chunkKey: string): Promise<ContentChunk> {
    try {
      console.log(`[MDXProvider] Loading chunk: ${chunkKey}`)

      // Dynamic import with static string for tree-shaking
      switch (chunkKey) {
        case 'blogs':
          return (await import('./static-content/static-content-blogs')).default
        case 'products':
          return (await import('./static-content/static-content-products')).default
        case 'case-studies':
          return (await import('./static-content/static-content-case-studies')).default
        default:
          console.warn(`[MDXProvider] Unknown chunk: ${chunkKey}`)
          return {}
      }
    } catch (error) {
      console.warn(`[MDXProvider] Failed to load chunk ${chunkKey}:`, error)
      return {}
    }
  }

  /**
   * Get chunk key for content type
   */
  private getChunkKey(type: ContentType): string {
    return this.normalizeContentType(type)
  }

  /**
   * Get URL path for content type
   */
  private getContentTypePath(type: ContentType): string {
    const pathMap: Record<ContentType, string> = {
      'blog': 'blogs',
      'product': 'products',
      'case-study': 'case-studies'
    }
    return pathMap[type]
  }

  /**
   * Normalize content type for internal use
   */
  private normalizeContentType(type: ContentType): string {
    // Convert case-study to case-studies for consistency
    return type === 'case-study' ? 'case-studies' : `${type}s`
  }

  /**
   * Generate content key for caching
   */
  private generateContentKey(type: ContentType, slug: string, locale: string): string {
    return `${type}-${slug}-${locale}`
  }

  /**
   * Filter and sort content list based on options
   */
  private filterAndSortContent<T extends ContentItem>(
    items: T[],
    options?: QueryOptions
  ): T[] {
    let filtered = [...items]

    // Apply filters
    if (options?.featured !== undefined) {
      filtered = filtered.filter(item => item.featured === options.featured)
    }

    if (options?.tags && options.tags.length > 0) {
      filtered = filtered.filter(item =>
        options.tags!.some(tag => item.tags?.includes(tag))
      )
    }

    // Apply sorting
    if (options?.sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[options.sortBy as keyof T] as any
        const bValue = b[options.sortBy as keyof T] as any

        if (options.order === 'desc') {
          return bValue > aValue ? 1 : -1
        } else {
          return aValue > bValue ? 1 : -1
        }
      })
    }

    // Apply pagination
    if (options?.limit) {
      const start = (options.offset || 0)
      filtered = filtered.slice(start, start + options.limit)
    }

    return filtered
  }

  /**
   * Validate content type
   */
  private isValidContentType(type: string): type is ContentType {
    return ['blog', 'product', 'case-study'].includes(type)
  }

  /**
   * Validate locale
   */
  private isValidLocale(locale: string): boolean {
    return ['en', 'zh'].includes(locale)
  }
}
