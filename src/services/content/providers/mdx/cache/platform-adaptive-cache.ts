/**
 * Platform Adaptive Cache for MDX Provider
 * 
 * This module provides a unified caching interface that automatically
 * selects the optimal cache implementation based on the deployment platform.
 * It ensures consistent caching behavior across Cloudflare Workers and Node.js environments.
 */

import { selectModuleByPlatform } from '@/lib/platform'
import { WorkersCache } from './workers-cache'
import { MemoryCache } from './memory-cache'
import type { CacheEntry } from '../types'

/**
 * Cache configuration interface
 */
interface CacheConfig {
  /** Default TTL in seconds */
  ttl: number
  /** Maximum cache size (for memory-based caches) */
  maxSize?: number
  /** Enable cache statistics */
  enableStats?: boolean
}

/**
 * Unified cache statistics interface
 */
interface CacheStats {
  /** Total number of cache entries */
  totalEntries: number
  /** Number of valid (non-expired) entries */
  validEntries: number
  /** Number of expired entries */
  expiredEntries: number
  /** Cache hit rate (0-1) */
  hitRate?: number
  /** Whether Cache API is available */
  hasCacheAPI?: boolean
  /** Approximate cache size in bytes */
  sizeBytes?: number
}

/**
 * Platform Adaptive Cache Implementation
 * 
 * This class provides a unified caching interface that automatically
 * selects between WorkersCache (for Cloudflare Workers) and MemoryCache
 * (for Node.js environments) based on the current platform.
 */
export class PlatformAdaptiveCache {
  private cache: WorkersCache | MemoryCache
  private config: Required<CacheConfig>
  
  constructor(config: Partial<CacheConfig> = {}) {
    // Merge with defaults
    this.config = {
      ttl: 3600,
      maxSize: 1000,
      enableStats: true,
      ...config
    }
    
    // Select appropriate cache implementation based on platform
    this.cache = selectModuleByPlatform<WorkersCache | MemoryCache>(
      new WorkersCache(this.config.ttl),
      new MemoryCache(this.config.ttl)
    )
    
    console.log(`[PlatformAdaptiveCache] Initialized with ${this.cache.constructor.name}`)
  }
  
  /**
   * Get item from cache
   * 
   * @param key - Cache key
   * @returns Cached data or null if not found/expired
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      if (this.cache instanceof WorkersCache) {
        return await this.cache.get<T>(key)
      } else {
        return this.cache.get<T>(key)
      }
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Get operation failed:', error)
      return null
    }
  }
  
  /**
   * Set item in cache
   * 
   * @param key - Cache key
   * @param data - Data to cache
   * @param ttl - Time to live in seconds (optional)
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    try {
      if (this.cache instanceof WorkersCache) {
        await this.cache.set(key, data, ttl)
      } else {
        this.cache.set(key, data, ttl)
      }
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Set operation failed:', error)
    }
  }
  
  /**
   * Check if key exists in cache (and is not expired)
   * 
   * @param key - Cache key
   * @returns True if key exists and is not expired
   */
  async has(key: string): Promise<boolean> {
    try {
      if (this.cache instanceof WorkersCache) {
        return await this.cache.has(key)
      } else {
        return this.cache.has(key)
      }
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Has operation failed:', error)
      return false
    }
  }
  
  /**
   * Delete item from cache
   * 
   * @param key - Cache key
   * @returns True if item was deleted
   */
  async delete(key: string): Promise<boolean> {
    try {
      if (this.cache instanceof WorkersCache) {
        return await this.cache.delete(key)
      } else {
        return this.cache.delete(key)
      }
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Delete operation failed:', error)
      return false
    }
  }
  
  /**
   * Clear all items from cache
   */
  async clear(): Promise<void> {
    try {
      if (this.cache instanceof WorkersCache) {
        await this.cache.clear()
      } else {
        this.cache.clear()
      }
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Clear operation failed:', error)
    }
  }
  
  /**
   * Get unified cache statistics
   * 
   * @returns Normalized cache statistics
   */
  getStats(): CacheStats {
    try {
      const rawStats = this.cache.getStats()

      if (this.cache instanceof WorkersCache) {
        const workersStats = rawStats as any
        return {
          totalEntries: workersStats.memoryEntries || 0,
          validEntries: workersStats.validEntries || 0,
          expiredEntries: workersStats.expiredEntries || 0,
          hitRate: workersStats.validEntries / (workersStats.validEntries + workersStats.expiredEntries) || 0,
          hasCacheAPI: workersStats.hasCacheAPI
        }
      } else {
        const memoryStats = rawStats as any
        return {
          totalEntries: memoryStats.totalEntries || 0,
          validEntries: memoryStats.validEntries || 0,
          expiredEntries: memoryStats.expiredEntries || 0,
          hitRate: memoryStats.hitRate || 0,
          sizeBytes: (this.cache as MemoryCache).getSize?.()
        }
      }
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Stats operation failed:', error)
      return {
        totalEntries: 0,
        validEntries: 0,
        expiredEntries: 0,
        hitRate: 0
      }
    }
  }
  
  /**
   * Cleanup expired entries (if supported by underlying cache)
   * 
   * @returns Number of entries removed
   */
  async cleanup(): Promise<number> {
    try {
      if (this.cache instanceof MemoryCache && this.cache.cleanup) {
        return this.cache.cleanup()
      }
      return 0
    } catch (error) {
      console.warn('[PlatformAdaptiveCache] Cleanup operation failed:', error)
      return 0
    }
  }
  
  /**
   * Get cache implementation name for debugging
   * 
   * @returns Name of the underlying cache implementation
   */
  getImplementationName(): string {
    return this.cache.constructor.name
  }
  
  /**
   * Get cache configuration
   * 
   * @returns Current cache configuration
   */
  getConfig(): Required<CacheConfig> {
    return { ...this.config }
  }
}
