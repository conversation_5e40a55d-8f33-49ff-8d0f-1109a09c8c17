/**
 * Platform Detection Utilities for MDX Provider
 *
 * This module provides utilities to detect and log deployment platform
 * information for debugging and monitoring purposes.
 */

import { targetPlatform, isCloudflareEnvironment, getRuntimeInfo } from '@/lib/platform'

/**
 * Check if static content files are available
 *
 * @returns True if static content files exist
 */
export function hasStaticContent(): boolean {
  try {
    // Try to resolve the static content module
    require.resolve('../static-content/static-content-blogs')
    return true
  } catch {
    return false
  }
}

/**
 * Get platform-specific capabilities
 *
 * @returns Object describing platform capabilities
 */
export function getPlatformCapabilities() {
  return {
    platform: targetPlatform,
    hasFileSystem: !isCloudflareEnvironment(),
    hasStaticContent: hasStaticContent(),
    supportsNodeAPIs: !isCloudflareEnvironment(),
    memoryLimited: isCloudflareEnvironment()
  }
}

/**
 * Log platform detection information
 */
export function logPlatformInfo(): void {
  const capabilities = getPlatformCapabilities()
  const runtimeInfo = getRuntimeInfo()

  console.log('[Platform Detector] Platform capabilities:', {
    platform: capabilities.platform,
    hasFileSystem: capabilities.hasFileSystem,
    hasStaticContent: capabilities.hasStaticContent,
    supportsNodeAPIs: capabilities.supportsNodeAPIs,
    memoryLimited: capabilities.memoryLimited,
    isExplicitlySet: runtimeInfo.isExplicitlySet
  })
}
