/**
 * Configuration for MDX Content Provider
 * 
 * This module exports the default configuration and configuration utilities
 * for the MDX content provider.
 */

import { DEFAULT_CONFIG } from './types'

export { DEFAULT_CONFIG }

/**
 * Validate MDX provider configuration
 * 
 * @param config - Configuration to validate
 * @returns Validated configuration
 * @throws Error if configuration is invalid
 */
export function validateConfig(config: any): void {
  if (config.cacheTTL && (typeof config.cacheTTL !== 'number' || config.cacheTTL < 0)) {
    throw new Error('cacheTTL must be a positive number')
  }

  if (config.contentDir && typeof config.contentDir !== 'string') {
    throw new Error('contentDir must be a string')
  }

  if (config.staticContentDir && typeof config.staticContentDir !== 'string') {
    throw new Error('staticContentDir must be a string')
  }
}

/**
 * Merge user configuration with defaults
 * 
 * @param userConfig - User-provided configuration
 * @returns Merged configuration
 */
export function mergeConfig(userConfig: any = {}): Required<typeof DEFAULT_CONFIG> {
  validateConfig(userConfig)
  
  return {
    ...DEFAULT_CONFIG,
    ...userConfig
  }
}
