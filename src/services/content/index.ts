/**
 * Content Service Public API
 * 
 * This is the main entry point for the content management service.
 * It provides a unified interface for all content-related operations.
 */

// Import core components
import { ContentService, ProviderFactory, ConfigManager } from './core'
import { defaultConfig } from './types'

// Initialize configuration
const config = ConfigManager.createFromSources(
  defaultConfig,
  ConfigManager.loadFromEnvironment(),
  ConfigManager.getEnvironmentConfig()
)

ConfigManager.initialize(config)

// Create a lazy-loaded content service instance
let _contentServiceInstance: ContentService | null = null

async function getContentService(): Promise<ContentService> {
  if (!_contentServiceInstance) {
    const provider = await ProviderFactory.create(config.provider, config.providerConfig)
    _contentServiceInstance = new ContentService(provider, config)
  }
  return _contentServiceInstance
}

// Export a proxy that handles async initialization
export const contentService = new Proxy({} as ContentService, {
  get(target, prop) {
    return async (...args: unknown[]) => {
      const service = await getContentService()
      const method = (service as any)[prop as string]
      if (typeof method === 'function') {
        return method.apply(service, args)
      }
      return method
    }
  }
})

// Export all types
export * from './types'

// Import modules that need wrapping
import {
  getAvailableLanguageVersions as _getAvailableLanguageVersions,
  handleContentLanguageSwitch as _handleContentLanguageSwitch,
  generateHreflangAttributes as _generateHreflangAttributes
} from './modules/language-switching'

import {
  generateStaticParams as _generateStaticParams,
  getAllContent as _getAllContent,
  getContentByLocale as _getContentByLocale,
  getContentStatistics as _getContentStatistics,
  validateContentForBuild as _validateContentForBuild
} from './modules/static-generation'

import {
  getRelatedContent as _getRelatedContent,
  getContentByTags as _getContentByTags,
  getFeaturedContent as _getFeaturedContent,
  getRecentContent as _getRecentContent,
  getContentByAuthor as _getContentByAuthor,
  getContentRecommendations as _getContentRecommendations
} from './modules/content-relations'

// Export wrapped versions of language switching functions
export const getAvailableLanguageVersions = async (
  contentType: import('./types').ContentType,
  slug: string
) => {
  const service = await getContentService()
  return _getAvailableLanguageVersions(service, contentType, slug)
}

export const handleContentLanguageSwitch = async (params: import('./modules').LanguageSwitchParams) => {
  const service = await getContentService()
  return _handleContentLanguageSwitch(service, params)
}

export const generateHreflangAttributes = async (
  contentType: import('./types').ContentType,
  slug: string,
  baseUrl: string = ''
) => {
  const service = await getContentService()
  return _generateHreflangAttributes(service, contentType, slug, baseUrl)
}

// Export wrapped versions of static generation functions
export const generateStaticParams = async (type: import('./types').ContentType) => {
  const service = await getContentService()
  return _generateStaticParams(service, type)
}

export const getAllContent = async <T extends import('./types').ContentItem>(type: import('./types').ContentType) => {
  const service = await getContentService()
  return _getAllContent<T>(service, type)
}

export const getContentByLocale = async <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string
) => {
  const service = await getContentService()
  return _getContentByLocale<T>(service, type, locale)
}

export const getContentStatistics = async () => {
  const service = await getContentService()
  return _getContentStatistics(service)
}

export const validateContentForBuild = async () => {
  const service = await getContentService()
  return _validateContentForBuild(service)
}

// Export wrapped versions of content relations functions
export const getRelatedContent = async <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  currentSlug: string,
  locale: string,
  limit?: number
) => {
  const service = await getContentService()
  return _getRelatedContent<T>(service, type, currentSlug, locale, limit)
}

export const getContentByTags = async <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  tags: string[],
  locale: string,
  limit?: number
) => {
  const service = await getContentService()
  return _getContentByTags<T>(service, type, tags, locale, limit)
}

export const getFeaturedContent = async <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string,
  limit?: number
) => {
  const service = await getContentService()
  return _getFeaturedContent<T>(service, type, locale, limit)
}

export const getRecentContent = async <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string,
  limit?: number
) => {
  const service = await getContentService()
  return _getRecentContent<T>(service, type, locale, limit)
}

export const getContentByAuthor = async <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  author: string,
  locale: string,
  limit?: number
) => {
  const service = await getContentService()
  return _getContentByAuthor<T>(service, type, author, locale, limit)
}

export const getContentRecommendations = async <T extends import('./types').ContentItem>(
  currentContent: import('./types').ContentItem,
  limit?: number
) => {
  const service = await getContentService()
  return _getContentRecommendations<T>(service, currentContent, limit)
}

// Export types and functions that don't need wrapping
export {
  type LanguageSwitchParams,
  type LanguageSwitchResult,
  generateSEOMetadata,
  generateListPageSEOMetadata,
  type SEOMetadata
} from './modules'

// Export utilities
export * from './utils'

// Export core classes for custom instantiation
export { ContentService, ProviderFactory, ConfigManager } from './core'

// Helper function to create a custom content service instance
export async function createContentService(customConfig?: Partial<import('./types').ContentServiceConfig>) {
  const mergedConfig = ConfigManager.createFromSources(
    defaultConfig,
    ConfigManager.loadFromEnvironment(),
    ConfigManager.getEnvironmentConfig(),
    customConfig || {}
  )
  
  ConfigManager.initialize(mergedConfig)
  const customProvider = await ProviderFactory.create(mergedConfig.provider, mergedConfig.providerConfig)
  return new ContentService(customProvider, mergedConfig)
}

// Helper function to get content title
export async function getContentTitle(
  type: import('./types').ContentType,
  slug: string,
  locale: string
): Promise<string | null> {
  const service = await getContentService()
  return service.getContentTitle(type, slug, locale)
}

// Helper function to get single content
export async function getContent<T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  slug: string,
  locale: string
): Promise<T | null> {
  const service = await getContentService()
  return service.getContent<T>(type, slug, locale)
}

// Helper function to get content list
export async function getContentList<T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string,
  options?: import('./types').QueryOptions
): Promise<T[]> {
  const service = await getContentService()
  return service.getContentList<T>(type, locale, options)
}

// Helper function to get all content slugs for static generation
export async function getAllContentSlugs(
  type: import('./types').ContentType
): Promise<Array<{ slug: string; locale: string }>> {
  const service = await getContentService()
  return service.getAllContentSlugs(type)
}
