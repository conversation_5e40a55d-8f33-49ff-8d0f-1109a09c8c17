/**
 * Provider Selector - Build-time Content Provider Selection
 *
 * This module implements build-time provider selection to optimize bundle size
 * and performance. The provider is determined at build time based on the
 * CONTENT_PROVIDER environment variable.
 *
 * Key Features:
 * - Build-time provider selection (no runtime overhead)
 * - Tree-shaking friendly (unused providers are eliminated)
 * - Type-safe provider imports
 * - Clear error messages for configuration issues
 * - Extensible architecture for future provider additions
 *
 * Current Provider:
 *
 * 1. MDX (default and only provider):
 *    - Best for: Cross-platform deployments (Cloudflare Workers, Vercel, Docker)
 *    - Pros: Platform-adaptive, zero configuration, Next.js native, excellent performance
 *    - Use cases: All deployment scenarios, optimized for both edge and traditional runtimes
 *
 * Future Extensibility:
 * The architecture supports adding new providers such as:
 * - Strapi (headless CMS)
 * - Sanity (real-time CMS)
 * - Notion (database-driven content)
 * - Custom providers
 *
 * Configuration:
 * Set CONTENT_PROVIDER environment variable to:
 * - 'mdx' (default)
 * - Future providers can be added here
 */

import type { ContentProvider } from '../types'
import type { ProviderConfig } from '../types'

// Provider selection happens at build time
const PROVIDER = process.env.CONTENT_PROVIDER || 'mdx'

/**
 * Get the content provider instance
 * 
 * This function uses dynamic imports with static strings to ensure
 * webpack can tree-shake unused providers at build time.
 * 
 * @param config - Optional provider-specific configuration
 * @returns Promise resolving to the provider instance
 */
export async function getContentProvider(
  config?: ProviderConfig
): Promise<ContentProvider> {
  console.log(`[Provider Selector] Loading provider: ${PROVIDER}`)
  
  try {
    switch (PROVIDER) {
      case 'mdx': {
        // Dynamic import with static path for tree-shaking
        const { MDXProvider } = await import('../providers/mdx')
        return new MDXProvider(config?.mdx)
      }

      // Future providers can be added here:
      // case 'strapi': {
      //   const { StrapiProvider } = await import('../providers/strapi')
      //   return new StrapiProvider(config?.strapi)
      // }
      // case 'sanity': {
      //   const { SanityProvider } = await import('../providers/sanity')
      //   return new SanityProvider(config?.sanity)
      // }

      default:
        throw new Error(
          `Unknown content provider: ${PROVIDER}. ` +
          `Currently supported: 'mdx'. ` +
          `Future providers: 'strapi', 'sanity', 'notion'.`
        )
    }
  } catch (error) {
    console.error(`[Provider Selector] Failed to load provider ${PROVIDER}:`, error)
    throw new Error(
      `Failed to load content provider '${PROVIDER}'. ` +
      `Make sure the provider is properly installed and configured.`
    )
  }
}

/**
 * Get the current provider name
 *
 * Useful for conditional logic based on the active provider
 *
 * @returns The provider name
 */
export function getProviderName(): string {
  return PROVIDER
}

/**
 * Check if a specific provider is active
 *
 * @param providerName - The provider name to check
 * @returns True if the provider is active
 */
export function isProvider(providerName: string): boolean {
  return PROVIDER === providerName
}

/**
 * Get list of supported providers
 *
 * @returns Array of supported provider names
 */
export function getSupportedProviders(): string[] {
  return ['mdx']
}

/**
 * Get list of planned future providers
 *
 * @returns Array of planned provider names
 */
export function getPlannedProviders(): string[] {
  return ['strapi', 'sanity', 'notion']
}

/**
 * Provider feature detection
 * 
 * Different providers support different features. This function
 * provides a way to check feature availability at build time.
 */
export const providerFeatures = {
  // Contentlayer2 features
  typeGeneration: PROVIDER === 'contentlayer2',
  buildTimeCompilation: PROVIDER === 'contentlayer2',
  hotReload: PROVIDER === 'contentlayer2',

  // MDX features
  edgeCompatible: PROVIDER === 'mdx',
  dynamicContent: PROVIDER === 'mdx',
  runtimeParsing: PROVIDER === 'mdx',
  platformAdaptive: PROVIDER === 'mdx',
  crossPlatformCompatible: PROVIDER === 'mdx',
  zeroConfiguration: PROVIDER === 'mdx',

  // Common features
  mdxSupport: true,
  i18nSupport: true,
  seoMetadata: true,
} as const

/**
 * Export provider info for client-side usage
 * 
 * This is mainly for debugging and conditional UI rendering
 */
export const providerInfo = {
  name: PROVIDER,
  features: providerFeatures,
} as const