// @ts-nocheck -- skip type checking
import * as docs_3 from "../content/docs/quickstart.zh.mdx?collection=docs&hash=1753165861823"
import * as docs_2 from "../content/docs/quickstart.mdx?collection=docs&hash=1753165861823"
import * as docs_1 from "../content/docs/index.zh.mdx?collection=docs&hash=1753165861823"
import * as docs_0 from "../content/docs/index.mdx?collection=docs&hash=1753165861823"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"index.mdx","absolutePath":"/home/<USER>/workspace/shipany-stwd/content/docs/index.mdx"}, data: docs_0 }, { info: {"path":"index.zh.mdx","absolutePath":"/home/<USER>/workspace/shipany-stwd/content/docs/index.zh.mdx"}, data: docs_1 }, { info: {"path":"quickstart.mdx","absolutePath":"/home/<USER>/workspace/shipany-stwd/content/docs/quickstart.mdx"}, data: docs_2 }, { info: {"path":"quickstart.zh.mdx","absolutePath":"/home/<USER>/workspace/shipany-stwd/content/docs/quickstart.zh.mdx"}, data: docs_3 }], [{"info":{"path":"meta.json","absolutePath":"/home/<USER>/workspace/shipany-stwd/content/docs/meta.json"},"data":{"pages":["quickstart"]}}, {"info":{"path":"meta.zh.json","absolutePath":"/home/<USER>/workspace/shipany-stwd/content/docs/meta.zh.json"},"data":{"pages":["quickstart"]}}])